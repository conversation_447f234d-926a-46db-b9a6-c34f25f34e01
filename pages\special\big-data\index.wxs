var format = {
  formattedNumber: function (num) {
    if (num === null || num === undefined) {
      return ""
    }
    return num.toLocaleString()
  },
  getMainTypeText: function (type) {
    if (type == "pay_num") {
      return "缴费"
    }
    if (type == "approved_num") {
      return "过审"
    }
    return "报名"
  },
  getCardClass: function (index) {
    var str = "orange-card"
    switch (index) {
      case 1:
        str = "blue-card"
        break
      case 2:
        str = "light-blue-card"
        break
      case 3:
        str = "cyan-card"
        break
    }
    return str
  },
}

module.exports = {
  formattedNumber: format.formattedNumber,
  getMainTypeText: format.getMainTypeText,
  getCardClass: format.getCardClass,
}
