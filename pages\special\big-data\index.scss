page {
  background: #f7f8fa;
}

// vant弹窗样式 - 确保覆盖底部固定区域
.van-popup {
  z-index: 10000 !important; // 比底部固定区域的999更高
}

.van-overlay {
  z-index: 9999 !important; // 遮罩层也需要高z-index
}

// vant选择器样式
.column-area {
  .van-picker-column__item {
    font-size: 28rpx;
    font-weight: 400;
    color: #999999;
  }

  view .active-item {
    color: #333333;
  }
}

.big-data {
  min-height: 100vh;
  position: relative;
  // padding-bottom: 166rpx; // 移除固定值，使用动态计算

  // 固定头部导航栏
  .fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: #ffffff;
    z-index: 99999;
    border-bottom: 1rpx solid #f0f0f0;
    transform: rotateZ(360deg);

    // 默认隐藏
    display: none;

    &.show {
      display: block !important;
    }

    &.hide {
      display: none !important;
    }

    .header-content {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 32rpx;

      .header-left {
        width: 100rpx;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 44px;

        .back-icon {
          width: 24rpx;
          height: 24rpx;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;

          // 用CSS绘制黑色返回箭头（向左）
          &::before {
            content: "";
            width: 16rpx;
            height: 16rpx;
            border: 0;
            border-left: 4rpx solid #000000;
            border-bottom: 4rpx solid #000000;
            transform: rotate(45deg);
          }
        }
      }

      .header-title {
        flex: 1;
        text-align: center;
        font-size: 34rpx;
        font-weight: bold;
        color: #000000;
        line-height: 44px;
      }

      .header-right {
        width: 100rpx;
      }
    }
  }
  .back-btn {
    position: absolute;
    top: 112rpx;
    left: 40rpx;
    width: 40rpx;
    height: 40rpx;
    z-index: 1000; // 提高层级，确保在所有内容之上
    pointer-events: auto; // 确保可以接收点击事件
  }
  .top-bg {
    width: 100%;
    height: 700rpx;
    position: relative;
    z-index: 0;
    .top-area {
      padding: 104rpx 40rpx 40rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      z-index: 1; // 设置较低的层级
      .top-title-img {
        width: 328rpx;
        height: 64rpx;
      }
      .title {
        font-size: 24rpx;
        color: #ffffff;
        margin-top: 16rpx;
      }
      .select-one {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 64rpx;
        margin-bottom: 26rpx;
      }
      .select-item {
        width: 322rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(64rpx);
        border-radius: 12rpx;
        border: 1rpx solid rgba(255, 255, 255, 0.4);
        transform: rotateZ(360deg);
        padding: 18rpx 20rpx 18rpx 24rpx;
        box-sizing: border-box;
        .text {
          color: #22242e;
          font-size: 26rpx;
        }
        .arrow {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
    .bg-img {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
    }
  }

  .main-contnet {
    width: 100%;
    margin-top: -200rpx;
    background: #ffffff;
    border-radius: 24rpx 24rpx 0 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: 16rpx;
    position: relative;
    z-index: 200;

    // Tab导航区域样式
    .tab-container {
      position: relative;
      width: 100%;
      background: #ffffff;
      border-bottom: 2rpx solid #ebecf0;

      .tab-scroll {
        width: 100%;
        white-space: nowrap;

        .tab-list {
          display: inline-flex;
          align-items: center;

          .tab-item {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 34rpx 40rpx 22rpx 40rpx;
            font-size: 28rpx;
            color: #666666;
            position: relative;
            white-space: nowrap;
            flex-shrink: 0;

            &.active {
              color: #22242e;
              font-weight: bold;
              font-size: 32rpx;

              &::after {
                content: "";
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 108rpx;
                height: 6rpx;
                background: #e60003;
              }
            }
          }
        }
      }
    }

    // 内容区域样式
    .content-container {
      flex: 1;
      width: 100%;

      .content-area {
        padding: 36rpx 32rpx 40rpx 32rpx;
        box-sizing: border-box;

        // 招聘公告标题
        .announcement-title {
          margin-bottom: 32rpx;
          .text {
            font-size: 26rpx;
            color: #5f7e95;
            vertical-align: middle;
          }
          .arrow {
            display: inline-block;
            width: 32rpx;
            height: 32rpx;
            margin-left: 4rpx;
            vertical-align: middle;
          }
        }

        // 统计卡片容器
        .stats-container {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 24rpx;
          margin-bottom: 32rpx;

          .stats-card {
            height: 192rpx;
            border-radius: 16rpx;
            padding: 24rpx 14rpx 24rpx 24rpx;
            box-sizing: border-box;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .card-title {
              font-size: 24rpx;
              margin-bottom: 16rpx;
              display: flex;
              align-items: center;
              justify-content: space-between;

              .arrow-icon {
                width: 32rpx;
                height: 32rpx;
              }
            }

            .card-number {
              display: flex;
              align-items: flex-end;
              margin-bottom: 8rpx;

              .number {
                font-size: 44rpx;
                font-weight: 400;
                font-family: "DINBold";
              }

              .unit {
                font-size: 24rpx;
                margin-left: 8rpx;
                margin-bottom: 8rpx;
              }

              .card-type {
                padding: 2rpx 8rpx;
                border-radius: 8rpx;
                font-size: 20rpx;
                margin-bottom: 8rpx;
                margin-left: 8rpx;
              }
            }

            .card-desc {
              margin-top: 8rpx;
              font-size: 22rpx;
            }

            // 橙色卡片 - 报名总人数
            &.orange-card {
              background: linear-gradient(315deg, #fff4f0 0%, #ffede8 100%);
              border: 1rpx solid #ffe5de;
              transform: rotateZ(360deg);

              .card-title {
                color: rgba(186, 87, 57, 0.7);
              }

              .card-number .number {
                color: #ba5739;
              }

              .card-number .unit {
                color: #ba5739;
              }
            }

            &.blue-card {
              background: linear-gradient(315deg, #f4f5ff 0%, #ebedff 100%);
              border: 1rpx solid #e5e7ff;
              transform: rotateZ(360deg);

              .card-title {
                color: rgba(81, 89, 159, 0.7);
              }

              .card-number .number {
                color: #51599f;
              }
              .card-number .unit {
                color: #51599f;
              }

              .card-type {
                background: rgba(255, 255, 255, 0.8);
                border: 1rpx solid rgba(81, 89, 159, 0.2);
                color: rgba(81, 89, 159, 0.8);
                transform: rotateZ(360deg);
              }
            }

            // 浅蓝色卡片 - 竞争激烈职位
            &.light-blue-card {
              background: linear-gradient(315deg, #f0f8ff 0%, #e8f0fe 100%);
              border: 1rpx solid #dfeafd;
              transform: rotateZ(360deg);

              .card-title {
                color: rgba(71, 109, 170, 0.7);
              }

              .card-number .number {
                color: #476daa;
              }

              .card-number .unit,
              .card-desc {
                color: rgba(71, 109, 170, 0.5);
              }

              .card-type {
                background: rgba(255, 255, 255, 0.8);
                border: 1rpx solid rgba(71, 109, 170, 0.2);
                color: rgba(71, 109, 170, 0.8);
                transform: rotateZ(360deg);
              }
            }

            &.cyan-card {
              background: linear-gradient(315deg, #f5fcff 0%, #ddf4ff 100%);
              border: 1rpx solid #d3f1ff;
              transform: rotateZ(360deg);

              .card-title {
                color: rgba(58, 125, 159, 0.7);
              }

              .card-number .number {
                color: #51599f;
              }

              .card-type {
                background: rgba(255, 255, 255, 0.8);
                border: 1rpx solid rgba(58, 125, 159, 0.2);
                color: rgba(58, 125, 159, 0.8);
                transform: rotateZ(360deg);
              }

              .card-number .unit,
              .card-number .desc-text,
              .card-desc {
                color: rgba(58, 125, 159, 0.5);
              }
            }
          }
        }

        .data-desc {
          margin-top: 20rpx;

          .data-desc-content {
            position: relative;

            &.collapsed {
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .data-desc-text {
              font-size: 24rpx;
              color: #919499;
              line-height: 1.6;
            }
          }

          .expand-btn {
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            margin-top: 8rpx;

            &.inline {
              margin-left: 8rpx;
              margin-top: 0;
            }

            .blue-text {
              font-size: 24rpx;
              color: #448aff;
            }

            .arrow-icon {
              width: 24rpx;
              height: 24rpx;
              margin-left: 4rpx;
              transition: transform 0.3s ease;

              &.rotated {
                transform: rotate(180deg);
              }
            }
          }
        }
      }
    }
  }
  // 职位数据表格区域
  .position-table-section {
    background: #ffffff;
    padding: 40rpx 32rpx;
    box-sizing: border-box;
    margin-top: 8rpx;

    // 标题区域
    .position-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx;

      .position-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #22242e;
      }

      .position-location {
        display: flex;
        align-items: center;
        border-radius: 24rpx;

        .location-text {
          font-size: 26rpx;
          color: #666666;
        }

        .location-arrow {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }

    // Tab导航容器
    .position-tab-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 32rpx;

      // 主Tab（热门/冷门）- 使用分段控制器样式
      .position-tab-list {
        display: flex;
        align-items: center;
        border-radius: 8rpx;
        overflow: hidden;
        width: fit-content;

        .position-tab-item {
          padding: 10rpx 20rpx;
          margin-right: 0;
          font-size: 24rpx;
          color: #666666;
          border: none;
          border-radius: 0;
          border: 2rpx solid #ebecf0;
          background: #f7f8fa;

          &.active {
            color: #ffffff;
            background: #e60003;
            border: 2rpx solid transparent;
          }
        }
      }

      // 子Tab（投报名数/投竞争比）- 独立按钮样式
      .position-sub-tab-list {
        display: flex;
        align-items: center;
        gap: 16rpx;
        width: fit-content;

        .position-sub-tab-item {
          padding: 10rpx 28rpx;
          font-size: 24rpx;
          color: #3c3d42;
          background: #ffffff;
          border: 1rpx solid #ebecf0;
          transform: rotateZ(360deg);
          border-radius: 8rpx;
          font-weight: 400;

          &.active {
            color: rgba(230, 0, 3, 0.8);
            background: rgba(230, 0, 3, 0.05);
            border: 1rpx solid rgba(230, 0, 3, 0.3);
            transform: rotateZ(360deg);
          }
        }
      }
    }

    // 表格内容
    .position-table-content {
      // 表格头部
      .position-table-header {
        display: flex;
        align-items: center;
        background: linear-gradient(
          180deg,
          #fef2f2 0%,
          rgba(254, 242, 242, 0.5) 100%
        );
        border-radius: 8rpx;
        padding: 20rpx 0 18rpx 0;
        .header-item {
          font-size: 24rpx;
          color: #93292c;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;
          margin: 0 8rpx;

          text {
            line-height: 1.2;
            margin: 2rpx 0;
          }

          &.header-rank {
            width: 80rpx;
            margin: 0;
          }

          &.header-position {
            flex: 1;
          }

          &.header-unit {
            width: 140rpx;
          }

          &.header-recruit {
            width: 120rpx;
          }

          &.header-apply {
            width: 120rpx;
          }
        }
      }

      // 表格主体
      .position-table-body {
        .position-table-row {
          display: flex;
          align-items: center;
          padding: 16rpx 0;
          border-bottom: 2rpx solid #f0f0f0;

          .table-item {
            font-size: 24rpx;
            color: #22242e;
            text-align: left;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            margin: 0 8rpx;

            &.table-rank {
              width: 80rpx;
              margin: 0;
              justify-content: center !important;

              .rank-badge {
                display: inline-block;
                font-size: 24rpx;
                color: #919499;

                &.rank-1 {
                  color: #e60003;
                  font-weight: 500;
                }

                &.rank-2 {
                  color: #ff6a4d;
                  font-weight: 500;
                }

                &.rank-3 {
                  color: #ffb474;
                  font-weight: 500;
                }
              }
            }

            &.table-position {
              flex: 1;

              .position-title {
                font-size: 24rpx;
                color: #448aff;
              }
            }

            &.table-unit {
              width: 140rpx;

              .unit-title {
                color: #3c3d42;
                font-size: 24rpx;
              }
            }

            &.table-recruit {
              width: 120rpx;

              .recruit-num {
                color: #3c3d42;
                font-size: 24rpx;
              }
            }

            &.table-apply {
              width: 120rpx;

              .apply-num {
                color: #3c3d42;
                font-size: 24rpx;
              }
            }
          }
        }
      }

      // 查看更多
      .load-more-section {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 32rpx;

        .load-more-text {
          font-size: 24rpx;
          color: #3c3d42;
        }

        .load-more-icon {
          width: 32rpx;
          height: 32rpx;
          margin-left: 4rpx;
        }
      }
    }
  }

  .recruiting-unit {
    margin-top: 16rpx;
    background: #ffffff;
    padding: 40rpx 32rpx;
    box-sizing: border-box;
    .recruit-title {
      color: #22242e;
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 32rpx;
    }
    // 表格内容
    .position-table-content-2 {
      // 表格头部
      .position-table-header {
        display: flex;
        align-items: center;
        background: linear-gradient(
          180deg,
          #fef2f2 0%,
          rgba(254, 242, 242, 0.5) 100%
        );
        border-radius: 8rpx;
        padding: 20rpx 0 18rpx 0;
        .header-item {
          font-size: 24rpx;
          color: #93292c;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;
          margin: 0 8rpx;

          text {
            line-height: 1.2;
            margin: 2rpx 0;
          }

          &.header-rank {
            width: 80rpx;
            margin: 0;
          }

          &.header-position {
            width: 200rpx;
          }

          &.header-unit {
            width: 100rpx;
          }

          &.header-recruit {
            width: 100rpx;
          }

          &.header-apply {
            width: 100rpx;
          }
        }
      }

      // 表格主体
      .position-table-body {
        .position-table-row {
          display: flex;
          align-items: center;
          padding: 16rpx 0;
          border-bottom: 2rpx solid #f0f0f0;

          .table-item {
            font-size: 24rpx;
            color: #22242e;
            text-align: left;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            margin: 0 8rpx;

            &.table-rank {
              width: 80rpx;
              margin: 0;
              justify-content: center !important;

              .rank-badge {
                display: inline-block;
                font-size: 24rpx;
                color: #919499;

                &.rank-1 {
                  color: #e60003;
                  font-weight: 500;
                }

                &.rank-2 {
                  color: #ff6a4d;
                  font-weight: 500;
                }

                &.rank-3 {
                  color: #ffb474;
                  font-weight: 500;
                }
              }
            }

            &.table-position {
              width: 200rpx;

              .position-title {
                font-size: 24rpx;
                color: #3c3d42;
              }
            }

            &.table-unit {
              width: 100rpx;

              .unit-title {
                color: #3c3d42;
                font-size: 24rpx;
              }
            }

            &.table-recruit {
              width: 100rpx;

              .recruit-num {
                color: #3c3d42;
                font-size: 24rpx;
              }
            }

            &.table-apply {
              width: 100rpx;

              .apply-num {
                color: #3c3d42;
                font-size: 24rpx;
              }
            }

            &.table-competition {
              width: 100rpx;

              .competition-rate {
                color: #ff5722;
                font-size: 24rpx;
                font-weight: 600;
              }
            }
          }
        }
      }

      // 查看更多
      .load-more-section {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 32rpx;

        .load-more-text {
          font-size: 24rpx;
          color: #3c3d42;
        }

        .load-more-icon {
          width: 32rpx;
          height: 32rpx;
          margin-left: 4rpx;
        }
      }
    }
    .zhaokao-bg {
      margin-top: 42rpx;
      width: 100%;
      height: 220rpx;
    }
  }
  .job-search {
    padding: 40rpx 32rpx;
    box-sizing: border-box;
    .title {
      font-size: 32rpx;
      color: #22242e;
      font-weight: bold;
    }
    .search-box {
      position: relative;
      margin-top: 32rpx;
      width: 100%;
      border-radius: 24rpx;
      padding: 8rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      z-index: 1;
      .bg {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        border-radius: 24rpx;
      }
      .top-area {
        padding: 0 16rpx;
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;
        .icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 8rpx;
        }
        .text {
          color: #ffffff;
          font-size: 28rpx;
          font-weight: 500;
        }
      }
      .white-box {
        position: relative;
        z-index: 2;
        flex: 1;
        width: 100%;
        border-radius: 16rpx;
        padding: 40rpx;
        box-sizing: border-box;
        .select-item {
          background: #f7f8fa;
          border-radius: 16rpx;
          border: 1rpx solid #ebecf0;
          transform: rotateZ(360deg);
          margin-bottom: 24rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 26rpx 24rpx 26rpx 32rpx;
          box-sizing: border-box;
          &:last-of-type {
            margin-bottom: 0;
          }

          // 不可编辑状态样式
          &.disabled {
            background: #f5f5f5;
            border-color: #e0e0e0;
            opacity: 0.6;
            pointer-events: none;

            .left {
              color: #999999 !important;
            }

            .right {
              opacity: 0.5;
            }
          }

          .left {
            display: flex;
            align-items: center;
            color: #3c3d42;
            font-size: 26rpx;
            flex: 1;
            .sp5 {
              width: 130rpx;
              display: flex;
              justify-content: space-between;
              align-items: center;
            }
          }
          .right {
            width: 32rpx;
            height: 32rpx;
          }

          // 输入框样式
          &.input-item {
            .left {
              .position-input {
                flex: 1;
                font-size: 26rpx;
                color: #3c3d42;
                background: transparent;
                border: none;
                outline: none;
                margin-top: 1px;

                .input-placeholder {
                  color: #919499;
                  font-size: 26rpx;
                }
              }
            }
          }
        }
        .button-row {
          margin-top: 40rpx;
          display: flex;
          align-items: center;
          gap: 24rpx;

          .clear-btn {
            flex: 1;
            height: 84rpx;
            background: rgba(230, 0, 3, 0.1);
            border-radius: 16rpx;
            color: #e60003;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28rpx;
            font-weight: 500;
          }

          .search-btn {
            flex: 2;
            height: 84rpx;
            background: #ec3e33;
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            font-size: 28rpx;
            font-weight: 500;

            .icon {
              width: 32rpx;
              height: 32rpx;
              margin-right: 8rpx;
            }
          }
        }
      }
    }
  }

  .result-area {
    padding: 40rpx 32rpx;
    box-sizing: border-box;
    margin-top: -40rpx;
    .top-area {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 32rpx;
      .left {
        display: flex;
        align-items: center;
        .text-area {
          font-size: 24rpx;
          color: #3c3d42;
          margin-right: 40rpx;
          .red {
            font-weight: 500;
            font-size: 24rpx;
            color: #ec3e33;
          }
        }
        .btn {
          padding: 12rpx 16rpx 12rpx 24rpx;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          background: #ffffff;
          border: 2rpx solid #ebecf0;
          border-radius: 12rpx;
          .text {
            color: #3c3d42;
            font-size: 24rpx;
          }
          .img {
            width: 32rpx;
            height: 32rpx;
          }
        }
      }
      .right {
        display: flex;
        align-items: center;
        .text {
          font-size: 24rpx;
          color: #448aff;
        }
        .icon {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }

  .bottom-box {
    padding: 14rpx 32rpx 14rpx 42rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      .icon-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 40rpx;
        .icon-img {
          width: 40rpx;
          height: 40rpx;
          margin-bottom: 4rpx;
        }
        .icon-text {
          color: #919499;
          font-size: 20rpx;
        }
      }
    }
    .right-btn {
      width: 400rpx;
      height: 84rpx;
      background: #ec3e33;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      font-size: 28rpx;
      color: #ffffff;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// 动画效果
@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 60rpx;
    opacity: 1;
  }
}

.w100 {
  width: 100% !important;
}
.area-box {
  margin-top: 16rpx;
  padding: 40rpx 32rpx;
  box-sizing: border-box;
  background: #ffffff;
  .title {
    font-size: 32rpx;
    color: #22242e;
    font-weight: bold;
    margin-bottom: 32rpx;
  }
  .baoming {
    width: 100%;
  }
}

.column-area {
  .van-picker-column__item {
    font-size: 30rpx;
    font-weight: 400;
    color: #313436;
  }

  view .active-item {
    font-weight: 500;
    color: #313436;
    font-size: 32rpx;
  }
}

.top-area {
  .van-picker__title {
    font-size: 32rpx;
    font-weight: 500;
    color: #313436;
  }

  .van-picker__confirm {
    font-size: 28rpx;
    font-weight: 400;
    color: #e60003;
  }

  .van-picker__cancel {
    font-size: 28rpx;
    font-weight: 400;
    color: #c2c5cc;
  }
}
.van-picker__toolbar {
  border-bottom: 2rpx solid #ebecf0;
}
