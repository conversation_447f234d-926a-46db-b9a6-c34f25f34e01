// pages/select/select-job/index.js
const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const {
  handleSingleSelect,
  handleMultiSelect,
} = require("@/services/selectionService")
const {
  setJobDetailSelectForTemplateCache,
  getJobDetailSelectForTemplateCache,
  getJobSelectForTemplateCache,
  setJobSelectForTemplateCache,
} = require("@/utils/cache/filterCache")
const APP = getApp()

Page({
  data: {
    show_white: true,

    // 当前选中的分类
    currentCategory: null,

    // 加载状态
    loading: {
      categories: false,
      options: false,
    },

    // 滚动定位
    scrollIntoView: "",

    // 筛选分类列表 - 二级显示结构（仅用于左侧显示）
    filterCategories: [],

    list: [],

    // 选中状态管理对象 - 类似 business-filter 的实现
    positionSelectForTemplate: {},
    positionSelectList: [],
    type: "",
    article_id: "",
    num: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    if (options.type) {
      this.setData({
        type: options.type,
      })
    }
    if (options.article_id) {
      this.setData({
        article_id: options.article_id,
      })
    }
    if (this.data.type == "detail") {
      // 初始化数据
      await this.loadFilterCategories()
    } else {
      // 初始化数据
      await this.getJobFilterMenu()
    }

    // 从页面参数中恢复已选择的筛选条件

    await this.updatePositionForTemplateFromCache()
    if (this.data.list.length > 0) {
      this.updatePositionSelectList(
        this.data.list,
        this.data.positionSelectForTemplate
      )
    }
  },

  onShow() {},
  onShareAppMessage() {},

  backPage() {
    if (getCurrentPages().length <= 1) {
      wx.reLaunch({
        url: "/pages/home/<USER>/index",
      })
      console.log("回首页")
      return false
    }
    console.log("触发返回")
    wx.navigateBack({
      delta: 1,
    })
  },
  async loadFilterCategories() {
    try {
      const res = await UTIL.request(API.getJobFilteList)
      if (res && res.error && res.error.code === 0 && res.data) {
        this.setData({
          list: res.data.list,
          isRequest: true,
        })
        // 初始化动态菜单
        if (res.data.list.length > 0) {
          this.initMenu(res.data.list)
        }
      }
    } catch (error) {}
  },
  async getJobFilterMenu() {
    try {
      const res = await UTIL.request(API.getJobFilterMenu)
      if (res && res.error && res.error.code === 0 && res.data) {
        const resData = res.data?.job_filter_menu
        this.setData({
          list: resData[resData?.length - 1].data,
          isRequest: true,
        })
        // 初始化动态菜单
        if (this.data.list.length > 0) {
          this.initMenu(this.data.list)
        }
      }
    } catch (error) {}
  },
  initMenu(list) {
    const updatedLeftCategories = []
    const positionSelectForTemplate = {}

    // 遍历接口返回的数据结构
    list.forEach((group) => {
      const categoryGroup = {
        id: group.key,
        name: group.title,
        children: [],
      }

      // 遍历每个分组下的筛选项
      group.list.forEach((filterItem) => {
        // 初始化 positionSelectForTemplate 中对应的 key
        positionSelectForTemplate[filterItem.filter_key] = []

        // 直接使用原数据结构
        categoryGroup.children.push({
          id: filterItem.filter_key,
          name: filterItem.title,
          ...filterItem, // 直接使用原有数据结构
        })
      })

      updatedLeftCategories.push(categoryGroup)
    })

    // 默认选中第一个分类
    let firstCategory = null
    if (
      updatedLeftCategories.length > 0 &&
      updatedLeftCategories[0].children.length > 0
    ) {
      firstCategory = updatedLeftCategories[0].children[0]
      firstCategory.selected = true
    }

    this.setData({
      filterCategories: updatedLeftCategories,
      currentCategory: firstCategory,
      positionSelectForTemplate: positionSelectForTemplate,
    })
  },
  async getArticleJobNum() {
    const apiParams = {
      ...this.buildApiParams(this.data.positionSelectForTemplate),
      article_id: this.data.article_id,
    }
    try {
      const res = await UTIL.request(API.getArticleJobNum, apiParams)
      if (res && res.error && res.error.code === 0 && res.data) {
        console.log(res)
        this.setData({
          num: res.data.num,
        })
        // this.setData({
        //   list: res.data.list,
        //   isRequest: true,
        // })
        // // 初始化动态菜单
        // if (res.data.list.length > 0) {
        //   this.initMenu(res.data.list)
        // }
      }
    } catch (error) {}
  },
  // 处理接口请求参数
  buildApiParams(selectedData) {
    console.log(selectedData, "拿到的")
    const apiParams = {}

    Object.keys(selectedData).forEach((keyName) => {
      const data =
        selectedData[keyName] || (keyName !== "filter_list" ? [] : {})
      apiParams[keyName] = data
    })
    console.log("得到的参数", apiParams)
    return UTIL.convertArraysToString(apiParams)
  },

  /**
   * 选择分类 - 滚动到对应位置
   */
  selectCategory(e) {
    const { category } = e.currentTarget.dataset

    this.setData({
      currentCategory: category,
      scrollIntoView: `category-${category.id}`,
    })
  },

  /**
   * 从缓存恢复选中状态
   */
  updatePositionForTemplateFromCache() {
    const positionSelectForTemplate = this.data.positionSelectForTemplate
    const { type } = this.data

    let cachePositionSelectForTemplate = {}

    // 根据 type 从不同缓存获取 filter_list
    if (type === "detail") {
      const jobDetailCache = getJobDetailSelectForTemplateCache()
      cachePositionSelectForTemplate = jobDetailCache.filter_list || {}
    } else if (type === "list") {
      const noticeCache = getJobSelectForTemplateCache()
      cachePositionSelectForTemplate = noticeCache.filter_list || {}
    }

    for (const key in positionSelectForTemplate) {
      if (cachePositionSelectForTemplate[key]) {
        positionSelectForTemplate[key] = cachePositionSelectForTemplate[key]
      }
    }
    this.setData({
      positionSelectForTemplate,
    })
  },
  /**
   * 处理选项点击事件 - 类似 group-list 组件的实现
   */
  handleOptionClick(e) {
    const { value, key } = e.currentTarget.dataset
    const { positionSelectForTemplate, positionSelectList } = this.data

    // 找到对应的筛选项配置
    const filterItem = this.findFilterItemByKey(this.data.list, key)
    if (!filterItem) {
      console.error("未找到筛选项配置:", key)
      return
    }

    const isMultipleChoice = filterItem.is_radio === 0
    const currentSelected = positionSelectForTemplate[key] || []

    // 根据选择类型调用对应的处理方法
    const updatedSelected = isMultipleChoice
      ? handleMultiSelect(currentSelected, value)
      : handleSingleSelect(currentSelected, value)

    // 更新选中状态
    this.setData({
      [`positionSelectForTemplate.${key}`]: updatedSelected,
    })
    if (this.data.type === "detail") {
      this.getArticleJobNum()
    }

    console.log(
      this.data.positionSelectForTemplate,
      "--------------------------------"
    )

    // 更新已选择列表
    this.updatePositionSelectList(
      this.data.list,
      this.data.positionSelectForTemplate
    )
  },

  /**
   * 移除已选择的筛选条件
   */
  removeSelectedFilter(e) {
    const { index } = e.currentTarget.dataset
    const { positionSelectList, positionSelectForTemplate } = this.data

    const removedFilter = positionSelectList[index]
    const { filter_key, value } = removedFilter

    // 从 positionSelectForTemplate 中移除该选项
    const currentSelected = positionSelectForTemplate[filter_key] || []
    const updatedSelected = currentSelected.filter(
      (selectedValue) => selectedValue != value
    )

    this.setData({
      [`positionSelectForTemplate.${filter_key}`]: updatedSelected,
    })

    // 更新已选择列表
    this.updatePositionSelectList(
      this.data.list,
      this.data.positionSelectForTemplate
    )
  },

  /**
   * 根据 filter_key 查找筛选项配置
   */
  findFilterItemByKey(list, filterKey) {
    for (let group of list) {
      for (let item of group.list) {
        if (item.filter_key === filterKey) {
          return item
        }
      }
    }
    return null
  },

  /**
   * 更新已选择列表
   */
  updatePositionSelectList(list, positionSelectForTemplate) {
    const selectList = []

    // 遍历所有筛选类型
    for (const filterKey in positionSelectForTemplate) {
      const selectedValues = positionSelectForTemplate[filterKey] || []

      if (selectedValues.length > 0) {
        // 找到对应的筛选项配置
        const filterItem = this.findFilterItemByKey(list, filterKey)
        if (!filterItem) continue

        // 遍历选中的值，直接使用原有字段
        selectedValues.forEach((value) => {
          const option = filterItem.list.find((item) => item.value == value)
          if (option) {
            selectList.push({
              ...option,
              filter_key: filterKey, // 只添加这个字段用于删除时识别类型
            })
          }
        })
      }
    }

    this.setData({
      positionSelectList: selectList,
    })
    console.log(
      this.data.positionSelectList,
      "--------------------------------"
    )
  },

  /**
   * 清空所有选择
   */
  clearAllSelected() {
    const clearedTemplate = {}
    for (const key in this.data.positionSelectForTemplate) {
      clearedTemplate[key] = []
    }

    this.setData({
      positionSelectForTemplate: clearedTemplate,
      positionSelectList: [],
    })
  },
  /**
   * 确认筛选条件
   */
  confirmFilters() {
    const { positionSelectForTemplate, type } = this.data

    // 根据 type 决定存储到哪个缓存
    if (type === "detail") {
      // 获取现有的 jobDetailSelectForTemplate 缓存
      const jobDetailCache = getJobDetailSelectForTemplateCache()
      // 将选中状态存储到 filter_list 字段
      jobDetailCache.filter_list = positionSelectForTemplate
      setJobDetailSelectForTemplateCache(jobDetailCache)
    } else if (type === "list") {
      // 获取现有的 noticeSelectForTemplate 缓存
      const noticeCache = getJobSelectForTemplateCache()
      // 将选中状态存储到 filter_list 字段
      noticeCache.filter_list = positionSelectForTemplate
      setJobSelectForTemplateCache(noticeCache)
    }

    wx.navigateBack()
  },
  /**
   * 恢复已选择筛选条件
   */
  restoreSelectedFilters(selectedFilters) {
    // 这里可以根据传入的筛选条件恢复状态
    // 具体实现取决于 selectedFilters 的数据格式
    console.log("恢复筛选条件:", selectedFilters)
  },

  onPageScroll(e) {
    if (e.scrollTop > 0) {
      if (!this.data.show_white) {
        this.setData({ show_white: true })
      }
    } else {
      if (this.data.show_white) {
        this.setData({ show_white: false })
      }
    }
  },
})
