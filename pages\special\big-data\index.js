const ROUTER = require("@/services/mpRouter")
const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
Page({
  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0, // 状态栏高度
    showFixedHeader: false, // 是否显示固定头部

    currentTab: 0,
    scrollLeft: 0,
    tabItemWidth: 0, // 初始化为0，后续获取实际宽度
    containerWidth: 0,

    // 职位数据表格相关
    currentMainTab: "hot", // 热门/冷门 tab
    selectedSubTab: "num", // 默认选中"按报名数"
    currentTableData: [], // 当前表格数据
    showLoadMore: true, // 是否显示查看更多按钮
    displayCount: 10, // 当前显示的数据条数
    
    // 数据说明展开收起
    shouldShowExpandButton: false, // 是否显示展开收起按钮

    jobData: {
      count: 0,
      list: [],
    },

    // 分页参数
    pageParams: {
      page: 1,
      size: 20,
      hasMore: true, // 是否还有更多数据
      loading: false, // 是否正在加载
    },

    // 是否已经进行过查询（用于判断是否可以触底加载）
    hasSearched: false,

    // vant选择器状态
    optPopShow: false,
    optTitle: "",
    optList: [],
    optKeys: [], // 选项对应的key数组
    value: [],
    defaultIndex: 0, // 默认选中索引
    currentSelector: "", // 当前激活的选择器类型

    // 选择器数据配置
    selectorConfig: {
      examType: {
        title: "选择考试类型",
        options: [],
        currentKey: "",
      },
      region: {
        title: "选择所属地区",
        options: [],
        currentKey: "",
      },
      specificExam: {
        title: "选择具体考试",
        options: [],
        currentKey: "",
      },
      searchRegion: {
        title: "选择地区",
        options: [],
        currentKey: "",
      },
      searchUnit: {
        title: "选择用人单位",
        options: [],
        currentKey: "",
      },
      positionLocation: {
        title: "选择地区",
        options: [],
        currentKey: "",
      },
    },

    projectList: [],

    // 选择器选项数据 - 从getOverView接口获取
    examTypeOptions: [],
    regionOptions: [],

    // 当前选中的key值
    selectedExamTypeKey: "",
    selectedRegionKey: "",
    project_id: "",
    selectedSearchUnitKey: "",
    region_id: "",

    // 显示用的文本值
    selectedExamTypeText: "考试类型",
    selectedRegionText: "所属地区",
    project_name: "重庆事业单位联考",
    selectedSearchRegionText: "全部地区",
    region_name: "",

    // 职位输入框的值
    positionInput: "",

    sortIndex: 0,

    // 数据说明展开收起状态
    dataDescExpanded: false,

    // 真实数据
    article_id: 1,
    project_id: 1,
    detailData: {},

    // 用人单位列表
    workList: [],

    // 新增：岗位报考查询地区选择器相关数据
    positionRegionSelector: {
      show: false, // 是否显示选择器
      type: "region", // 选择器类型
      title: "请选择地区", // 选择器标题
      value: [], // 选择器当前选中值（索引数组）
      columns: [], // 选择器列数据
      level: 1, // 当前可编辑级数：1=三级联动, 2=市区二级, 3=只选区, 4=不可编辑
      editable: true, // 是否可编辑

      // 显示文本和值
      displayText: "全部地区",
      selectedValues: [], // 存储选中的[省id, 市id, 区id]
      selectedIndexes: [], // 存储选中的每级下标，用于回显：level=1时长度为3，level=2时长度为2，level=3时长度为1

      // 根据level预设的省市信息
      presetProvince: null, // level=2,3,4时的预设省份
      presetCity: null, // level=3,4时的预设城市
      presetDistrict: null, // level=4时的预设区县
    },
    // 新增：地区数据缓存，key为parent_id，value为对应的子级列表
    regionDataCache: {},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取设备信息
    this.getSystemInfo()
    
    // 从路由参数中获取project_id和article_id
    const routeProjectId = options.project_id || options.projectId
    const routeArticleId = options.article_id || options.articleId
    
    if (routeProjectId) {
      this.setData({ project_id: routeProjectId })
    }
    if (routeArticleId) {
      this.setData({ article_id: routeArticleId })
    }
    
    // 使用统一的初始化方法
    this.getOverView()
  },
  
  /**
   * 统一的初始化方法，与H5保持一致
   */
  async getOverView(flag) {
    try {
      // 清空职位搜索数据
      this.setData({
        jobData: {
          count: 0,
          list: []
        }
      })
      
      const param = {
        project_id: this.data.project_id,
        article_id: this.data.article_id,
      }
      
      const res = await UTIL.request(API.getOverView, param)
      if (res?.data) {
        const data = res.data
        
        // 设置详情数据
        this.setData({
          detailData: data.detail || {},
          updateTime: data.detail?.last_update_time || ''
        }, () => {
          // 在setData完成后检查文本是否需要展开收起按钮
          this.checkTextOverflow()
        })
        
        // 设置考试类型选项 - 从getOverView接口获取
        if (data.examtype_list && data.examtype_list.length) {
          // 设置考试类型选择器选项
          const examTypeOptions = data.examtype_list.map(item => ({
            text: item.type_name,
            value: item.id
          }))
          
          // 设置当前选中的考试类型
          const selectedExamType = data.examtype_list.find(item => item.id == data.exam_type)
          if (selectedExamType) {
            this.setData({
              examTypeOptions,
              selectedExamTypeText: selectedExamType.type_name,
              selectedExamTypeKey: selectedExamType.id
            })
          }
        }
        
        // 设置地区选项 - 从getOverView接口获取
        if (data.province_list && data.province_list.length) {
          // 设置地区选择器选项
          const regionOptions = data.province_list.map(item => ({
            text: item.area_name,
            value: item.id
          }))
          
          // 设置当前选中的地区
          const selectedRegion = data.province_list.find(item => item.id == data.region_province)
          if (selectedRegion) {
            this.setData({
              regionOptions,
              selectedRegionText: selectedRegion.area_name,
              selectedRegionKey: selectedRegion.id
            })
          }
        }
        
        // 设置项目信息
        if (data.project_list && data.project_list.length) {
          const projectList = data.project_list
          let selectedProject = null
          
          // 如果有路由参数，优先使用路由参数
          if (this.data.project_id) {
            selectedProject = projectList.find(item => item.id == this.data.project_id)
          }
          
          // 如果没有找到或没有路由参数，使用第一个
          if (!selectedProject) {
            selectedProject = projectList[0]
          }
          
          this.setData({
            projectList,
            project_id: selectedProject.id,
            project_name: selectedProject.name
          })
        }
        
        if (!flag) {
          // 设置文章信息
          if (data.article_list && data.article_list.length) {
            const articleList = data.article_list.map(item => ({
              id: item.id,
              title: item.title
            }))
            
            let selectedArticle = null
            let selectedArticleIndex = 0
            
            // 如果有路由参数，优先使用路由参数
            if (this.data.article_id) {
              selectedArticleIndex = articleList.findIndex(item => item.id == this.data.article_id)
              if (selectedArticleIndex >= 0) {
                selectedArticle = articleList[selectedArticleIndex]
              }
            }
            
            // 如果没有找到或没有路由参数，使用第一个
            if (!selectedArticle) {
              selectedArticle = articleList[0]
              selectedArticleIndex = 0
            }
            
            this.setData({
              articleList,
              article_id: selectedArticle.id,
              currentTab: selectedArticleIndex
            })
          }
        }
        
        // 设置地区信息（用于职位表格筛选）
        if (data.detail?.region_num_list?.length) {
          let regionList = [...data.detail.region_num_list]
          if (regionList.length > 1) {
            const allOption = {
              area_name: "全部",
              id: ""
            }
            regionList.unshift(allOption)
          }
          
          this.setData({
            "detailData.region_num_list": regionList,
            region_name: regionList[0].area_name,
            region_id: regionList[0].id
          })
        }
        
        // 设置地区选择器状态
        await this.setupPositionRegionSelector()
        
        // 获取用人单位列表
        const workParam = {
          region_province: data.detail?.region_province || "",
          region_city: data.detail?.region_city || "",
          region_district: data.detail?.region_district || "",
          article_id: this.data.article_id,
        }
        
        // 根据level设置工作参数
        if (!workParam.region_province && !workParam.region_city && !workParam.region_district) {
          switch (data.detail?.level) {
            case 2:
              workParam.region_province = data.detail.region_province
              break
            case 3:
              workParam.region_province = data.detail.region_province
              workParam.region_city = data.detail.region_city
              break
            case 4:
              workParam.region_province = data.detail.region_province
              workParam.region_city = data.detail.region_city
              workParam.region_district = data.detail.region_district
              break
          }
        }
        
        await this.getWorkList(workParam)
        await this.getCurrentTableData()
        
        // 预加载地区数据
        this.preloadRegionData(data.detail)
      }
    } catch (error) {
      console.error('初始化数据失败:', error)
      // 如果统一接口失败，回退到原有的分步获取方式
      this.getProjectList()
    }
  },
  
  // 获取考试项目列表
  async getProjectList() {
    const param = {
      province: this.data.selectedRegionKey,
      exam_type: this.data.selectedExamTypeKey,
    }
    const res = await UTIL.request(API.getProjectList, param)
    if (res) {
      const projectList = res?.data || []
      const project_id = projectList.length ? projectList[0].id : 1
      const project_name = projectList.length ? projectList[0].name : ""
      this.setData({
        projectList,
        project_id,
        project_name,
      })
      await this.getArticleList()
    }
  },
  async getArticleList() {
    const param = {
      project_id: this.data.project_id,
    }
    const res = await UTIL.request(API.getArticleList, param)
    if (res) {
      const articleList = res?.data || []
      const article_id = articleList.length ? articleList[0].id : 1
      this.setData({
        articleList,
        article_id,
      })
      // 统一使用getOverView方法
      this.getOverView()
    }
  },

  // 根据地区获取用人单位
  async getWorkList(param) {
    param.article_id = this.data.article_id
    const res = await UTIL.request(API.getWorkUnitList, param)
    if (res) {
      const workList = res?.data || []
      this.setData({
        workList,
      })
    }
  },

  /**
   * 获取设备信息
   */
  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync()

    // 计算导航栏相关高度
    const statusBarHeight = systemInfo.statusBarHeight || 0

    this.setData({
      statusBarHeight: statusBarHeight,
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 初始化滚动条
    setTimeout(() => {
      this.initScrollBar()
    }, 100)
  },

  /**
   * 初始化滚动条
   */
  initScrollBar() {
    const query = wx.createSelectorQuery()

    // 先获取容器宽度
    query
      .select(".tab-container")
      .boundingClientRect((containerRect) => {
        if (containerRect) {
          // 再获取tab项宽度
          const itemQuery = wx.createSelectorQuery()
          itemQuery
            .select(".tab-item")
            .boundingClientRect((itemRect) => {
              if (itemRect) {
                this.setData(
                  {
                    containerWidth: containerRect.width,
                    tabItemWidth: itemRect.width,
                  },
                  () => {
                    this.updateScrollBar()
                  }
                )
              } else {
                // 如果无法获取，使用默认计算值
                const systemInfo = wx.getSystemInfoSync()
                const defaultItemWidth = (180 * systemInfo.windowWidth) / 750 // rpx转px
                this.setData(
                  {
                    containerWidth: containerRect.width,
                    tabItemWidth: defaultItemWidth,
                  },
                  () => {
                    this.updateScrollBar()
                  }
                )
              }
            })
            .exec()
        }
      })
      .exec()
  },

  /**
   * 更新滚动条位置 - 修复双向滚动问题
   */
  updateScrollBar() {
    const { currentTab, articleList, containerWidth, tabItemWidth } = this.data

    if (!containerWidth || !tabItemWidth) return

    const totalWidth = articleList?.length * tabItemWidth || 0

    // 只有当总宽度大于容器宽度时才需要滚动
    if (totalWidth > containerWidth) {
      // 计算当前tab的中心位置
      const currentTabCenter = currentTab * tabItemWidth + tabItemWidth / 2

      // 计算滚动位置，让当前tab居中
      let targetScrollLeft = currentTabCenter - containerWidth / 2

      // 边界处理
      const maxScrollLeft = totalWidth - containerWidth
      targetScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft))

      this.setData({
        scrollLeft: targetScrollLeft,
      })
    }
  },

  /**
   * Tab点击事件
   */
  onTabClick(e) {
    const { index, id } = e.currentTarget.dataset

    this.setData({
      currentTab: index,
      article_id: id,
    })
    
    // 统一使用getOverView方法
    this.getOverView()

    // 延迟执行滚动，确保数据更新完成
    setTimeout(() => {
      this.updateScrollBar()
    }, 50)
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 初始化胶囊按钮颜色
    if (this.data.showFixedHeader) {
      // 白色背景时，胶囊按钮显示黑色
      wx.setNavigationBarColor({
        frontColor: "#000000",
        backgroundColor: "#ffffff",
      })
    } else {
      // 深色背景时，胶囊按钮显示白色
      wx.setNavigationBarColor({
        frontColor: "#ffffff",
        backgroundColor: "#000000",
      })
    }
  },

  /**
   * 页面滚动事件
   */
  onPageScroll(e) {
    const scrollTop = e.scrollTop
    const showFixedHeader = scrollTop > 10 // 滚动超过10rpx显示固定头部

    if (showFixedHeader !== this.data.showFixedHeader) {
      if (!showFixedHeader) {
        wx.setNavigationBarColor({
          frontColor: "#ffffff",
          backgroundColor: "#000000",
        })
      } else {
        wx.setNavigationBarColor({
          frontColor: "#000000",
          backgroundColor: "#ffffff",
        })
      }
      this.setData({
        showFixedHeader: showFixedHeader,
      })
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: "分享标题",
      path: "/pages/special/big-data/index",
      imageUrl: "/path/to/share/image.jpg",
    }
  },

  /**
   * 主Tab点击事件（热门/冷门）
   */
  onMainTabClick(e) {
    const tabType = e.currentTarget.dataset.index
    this.setData(
      {
        currentMainTab: tabType,
        displayCount: 10, // 重置显示数量
      },
      () => {
        this.getCurrentTableData()
      }
    )
  },

  /**
   * 子Tab点击事件（按报名数/按竞争比） - 单选模式
   */
  onSubTabClick(e) {
    const type = e.currentTarget.dataset.type
    // 直接设置选中项
    this.setData(
      {
        selectedSubTab: type,
        displayCount: 10, // 重置显示数量
      },
      () => {
        this.getCurrentTableData()
      }
    )
  },

  /**
   * 获取当前表格数据
   */
  async getCurrentTableData() {
    const param = {
      region_id: this.data.region_id,
      project_id: this.data.project_id,
      article_id: this.data.article_id,
      type: this.data.selectedSubTab,
      heat: this.data.currentMainTab,
    }
    const res = await UTIL.request(API.getStatisticsList, param)

    const fullData = res?.data || []
    const result = fullData.slice(0, this.data.displayCount)

    // 更新是否显示"查看更多"按钮
    this.setData({
      showLoadMore: fullData.length > this.data.displayCount,
      currentTableData: result,
    })
  },

  /**
   * 显示选择器
   */
  showSelector(type) {
    const config = this.data.selectorConfig[type]
    if (config) {
      // 将options转换为vant picker需要的格式
      let optList = config.options.map((item) => item.name)
      let optKeys = config.options.map((item) => item.key)

      // 获取当前选中的key并找到对应的索引
      let currentKey = ""
      switch (type) {
        case "examType":
          // 使用从getOverView接口获取的考试类型数据
          optList = this.data.examTypeOptions.map(item => item.text)
          optKeys = this.data.examTypeOptions.map(item => item.value)
          currentKey = this.data.selectedExamTypeKey
          break
        case "region":
          // 使用从getOverView接口获取的地区数据
          optList = this.data.regionOptions.map(item => item.text)
          optKeys = this.data.regionOptions.map(item => item.value)
          currentKey = this.data.selectedRegionKey
          break
        case "specificExam":
          optList = this.data.projectList.map((item) => item.name)
          optKeys = this.data.projectList.map((item) => item.id)
          currentKey = this.data.project_id
          break
        case "searchUnit":
          if (this.data.workList.length) {
            optList = this.data.workList.map((item) => item.work_unit)
            optKeys = this.data.workList.map((item) => item.work_unit)
          }
          currentKey = this.data.selectedSearchUnitKey
          break
        case "positionLocation":
          if (
            this.data.detailData.region_apply_num_list &&
            this.data.detailData.region_apply_num_list.length
          ) {
            optList = this.data.detailData.region_apply_num_list.map(
              (item) => item.area_name
            )
            optKeys = this.data.detailData.region_apply_num_list.map(
              (item) => item.id
            )
          }
          currentKey = this.data.region_id
          break
      }

      // 根据key找到对应的索引
      const currentIndex = optKeys.findIndex((key) => key === currentKey)
      const value = currentIndex >= 0 ? [currentIndex] : [0] // 如果找不到则默认选中第一项

      this.setData({
        currentSelector: type,
        optPopShow: true,
        optTitle: config.title,
        optList: optList,
        optKeys: optKeys, // 保存keys数组
        value: value,
        defaultIndex: currentIndex >= 0 ? currentIndex : 0, // 设置默认选中索引
      })
    }
  },

  /**
   * 考试类型点击事件
   */
  onExamTypeClick() {
    this.showSelector("examType")
  },

  /**
   * 所属地区点击事件
   */
  onRegionClick() {
    this.showSelector("region")
  },

  /**
   * 具体考试点击事件
   */
  onSpecificExamClick() {
    this.showSelector("specificExam")
  },

  /**
   * 搜索地区点击事件
   */
  onSearchRegionClick() {
    this.showSelector("searchRegion")
  },

  /**
   * 搜索单位点击事件
   */
  onSearchUnitClick() {
    this.showSelector("searchUnit")
  },

  /**
   * 职位地区点击事件
   */
  onPositionLocationClick() {
    this.showSelector("positionLocation")
  },

  /**
   * 新增：岗位报考查询地区选择器点击事件
   */
  onPositionRegionClick() {
    // 根据level判断是否可编辑
    if (!this.data.positionRegionSelector.editable) {
      return
    }

    this.showPositionRegionSelector()
  },

  /**
   * 新增：显示岗位报考查询地区选择器
   */
  async showPositionRegionSelector() {
    const { level, selectedIndexes } = this.data.positionRegionSelector
    const { detailData } = this.data

    // 根据level生成对应的列数据
    let columns = []
    let title = "请选择地区"
    let defaultIndex = []

    try {
      switch (level) {
        case 4:
          return

        case 3:
          // level=3: 只能选择区县，直接通过detailData.region_city获取
          title = "请选择区县"
          if (detailData?.region_city) {
            const districts = await this.getRegionDataWithCache(
              detailData.region_city,
              3
            )
            if (districts && districts.length > 0) {
              columns = [{ values: districts }]

              // 使用selectedIndexes进行回显
              if (selectedIndexes && selectedIndexes.length === 1) {
                defaultIndex = [selectedIndexes[0]]
              } else {
                defaultIndex = [0]
              }
            } else {
              wx.showToast({ title: "暂无区县数据", icon: "none" })
              return
            }
          } else {
            wx.showToast({ title: "缺少城市信息", icon: "none" })
            return
          }
          break

        case 2:
          // level=2: 二级联动（市区），第一级通过detailData.region_province获取
          title = "请选择市区"
          if (detailData?.region_province) {
            const cities = await this.getRegionDataWithCache(
              detailData.region_province,
              2
            )
            if (cities && cities.length > 0) {
              // 使用selectedIndexes进行回显，如果没有则使用第一条
              let initialCityIndex = 0
              if (selectedIndexes && selectedIndexes.length === 2) {
                initialCityIndex = selectedIndexes[0]
              }

              // 获取第二级数据（区县）
              const cityId = cities[initialCityIndex]?.value || cities[0]?.value
              const districts = await this.getRegionDataWithCache(cityId, 3)

              let initialDistrictIndex = 0
              if (selectedIndexes && selectedIndexes.length === 2) {
                initialDistrictIndex = selectedIndexes[1]
              }

              columns = [{ values: cities }, { values: districts || [] }]
              defaultIndex = [initialCityIndex, initialDistrictIndex]
            } else {
              wx.showToast({ title: "暂无城市数据", icon: "none" })
              return
            }
          } else {
            wx.showToast({ title: "缺少省份信息", icon: "none" })
            return
          }
          break

        case 1:
        default:
          // level=1: 三级联动，parent_id=0获取第一级
          title = "请选择地区"

          const provinces = await this.getRegionDataWithCache(0, 1)
          if (provinces && provinces.length > 0) {
            // 使用selectedIndexes进行回显，如果没有则使用第一条
            let initialProvinceIndex = 0
            let initialCityIndex = 0
            let initialDistrictIndex = 0

            if (selectedIndexes && selectedIndexes.length === 3) {
              initialProvinceIndex = selectedIndexes[0]
              initialCityIndex = selectedIndexes[1]
              initialDistrictIndex = selectedIndexes[2]
            }

            // 获取第二级数据（城市）
            const provinceId =
              provinces[initialProvinceIndex]?.value || provinces[0]?.value
            const cities = await this.getRegionDataWithCache(provinceId, 2)

            // 获取第三级数据（区县）
            const cityId =
              cities?.[initialCityIndex]?.value || cities?.[0]?.value
            const districts = cityId
              ? await this.getRegionDataWithCache(cityId, 3)
              : []

            columns = [
              { values: provinces },
              { values: cities || [] },
              { values: districts || [] },
            ]
            defaultIndex = [
              initialProvinceIndex,
              initialCityIndex,
              initialDistrictIndex,
            ]
          } else {
            wx.showToast({ title: "暂无省份数据", icon: "none" })
            return
          }
          break
      }

      this.setData({
        "positionRegionSelector.show": true,
        "positionRegionSelector.title": title,
        "positionRegionSelector.columns": columns,
      })

      // 设置默认选中项
      setTimeout(() => {
        const picker = this.selectComponent("#positionRegionPicker")
        if (picker && picker.setIndexes && defaultIndex.length > 0) {
          picker.setIndexes(defaultIndex)
        }
      }, 200)
    } catch (error) {
      console.error("获取地区数据失败:", error)
      wx.showToast({ title: "获取地区数据失败", icon: "none" })
    }
  },

  /**
   * 新增：通过缓存获取地区数据
   */
  async getRegionDataWithCache(parentId, dataLevel = null) {
    // 先检查缓存
    if (
      this.data.regionDataCache.hasOwnProperty(parentId) &&
      this.data.regionDataCache[parentId]
    ) {
      return this.data.regionDataCache[parentId]
    }

    // 缓存中没有，则请求接口
    const data = await this.getRegionData(parentId, dataLevel)

    // 将数据存入缓存
    this.data.regionDataCache[parentId] = data

    return data
  },

  /**
   * 新增：通过接口获取地区数据
   */
  async getRegionData(parentId, dataLevel = null) {
    // 根据parentId和上下文推断应该传递的level值
    let apiLevel = dataLevel || this.getApiLevelByParentId(parentId)

    const param = {
      article_id: this.data.article_id,
      level: apiLevel,
      parent_id: parentId,
    }

    try {
      const res = await UTIL.request(API.getRegionListByArticleId, param)
      if (res && res.data) {
        // 将接口数据转换为选择器需要的格式
        return res.data.map((item) => ({
          text: item.area_name || item.name,
          value: item.id,
          ...item,
        }))
      }
      return []
    } catch (error) {
      console.error("请求地区数据失败:", error)
      return []
    }
  },

  /**
   * 新增：根据parent_id推断应该传递给接口的level值
   */
  getApiLevelByParentId(parentId) {
    // parent_id = 0 表示获取省级数据，接口level = 1
    if (parentId === 0 || parentId === "0") {
      return 1
    }

    // 检查是否在已知的省级数据中
    const provinceData =
      this.data.regionDataCache[0] || this.data.regionDataCache["0"]
    if (provinceData && provinceData.some((item) => item.value == parentId)) {
      // 这是省级id，获取市级数据，接口level = 2
      return 2
    }

    // 检查是否在已知的市级数据中
    for (let cacheKey in this.data.regionDataCache) {
      if (cacheKey !== "0" && cacheKey !== 0) {
        const cacheData = this.data.regionDataCache[cacheKey]
        if (cacheData && cacheData.some((item) => item.value == parentId)) {
          // 这是市级id，获取区级数据，接口level = 3
          return 3
        }
      }
    }

    // 无法推断，默认使用组件的level + 1（因为是获取下一级数据）
    const componentLevel = this.data.positionRegionSelector.level
    return Math.min(componentLevel + 1, 3)
  },

  /**
   * 新增：岗位报考查询地区选择器变化事件
   */
  async onPositionRegionSelectorChange(e) {
    const { picker, value, index } = e.detail
    const { level } = this.data.positionRegionSelector

    try {
      // 根据level处理不同的联动逻辑
      if (level === 1) {
        // 完整三级联动
        if (index === 0) {
          // 省份变化，更新市和区
          const selectedProvince = value[0]
          const cities = await this.getRegionDataWithCache(
            selectedProvince.value,
            2
          )

          // 获取第一个城市的区县数据
          let districts = []
          if (cities && cities.length > 0) {
            districts = await this.getRegionDataWithCache(cities[0].value, 3)
          }

          picker.setColumnValues(1, cities || [])
          picker.setColumnValues(2, districts || [])
        } else if (index === 1) {
          // 城市变化，更新区
          const selectedCity = value[1]
          const districts = await this.getRegionDataWithCache(
            selectedCity.value,
            3
          )

          picker.setColumnValues(2, districts || [])
        }
      } else if (level === 2) {
        // 市区二级联动
        if (index === 0) {
          // 城市变化，更新区
          const selectedCity = value[0]
          const districts = await this.getRegionDataWithCache(
            selectedCity.value,
            3
          )

          picker.setColumnValues(1, districts || [])
        }
      }
      // level=3只有一列，不需要联动处理
    } catch (error) {
      console.error("联动获取地区数据失败:", error)
      wx.showToast({ title: "获取地区数据失败", icon: "none" })
    }
  },

  /**
   * 新增：根据地区值和level获取对应的索引数组
   */
  getRegionIndexesByValues(values, level) {
    const regionData = APP.globalData?.serverConfig?.regionData || []
    if (!values || values.length === 0 || !regionData) return []

    const [provinceId, cityId, districtId] = values
    let indexes = []

    // 根据level决定需要计算哪几级的索引
    if (level >= 1 && provinceId) {
      const provinceIndex = regionData.findIndex((p) => p.id == provinceId)
      if (provinceIndex >= 0) {
        indexes.push(provinceIndex)

        if (level >= 2 && cityId) {
          const province = regionData[provinceIndex]
          const cityIndex =
            province.child?.findIndex((c) => c.id == cityId) ?? -1
          if (cityIndex >= 0) {
            indexes.push(cityIndex)

            if (level >= 3 && districtId) {
              const city = province.child[cityIndex]
              const districtIndex =
                city.child?.findIndex((d) => d.id == districtId) ?? -1
              if (districtIndex >= 0) {
                indexes.push(districtIndex)
              }
            }
          }
        }
      }
    }

    return indexes
  },

  /**
   * 新增：根据detailData中的level和地区信息设置地区选择器状态
   */
  async setupPositionRegionSelector() {
    const { detailData } = this.data
    if (!detailData) return

    const level = detailData.level

    let displayText = "全部地区"
    let selectedValues = []
    let editable = true

    // 根据level设置不同的状态
    switch (level) {
      case 4:
        // level=4: 不可编辑，需要显示具体的省-市-区文案
        editable = false

        // 使用接口获取显示文案
        if (
          detailData.region_province &&
          detailData.region_city &&
          detailData.region_district
        ) {
          try {
            // 获取省份数据
            const provinces = await this.getRegionDataWithCache(0, 1)
            const province = provinces?.find(
              (p) => p.value == detailData.region_province
            )

            // 获取城市数据
            const cities = await this.getRegionDataWithCache(
              detailData.region_province,
              2
            )
            const city = cities?.find((c) => c.value == detailData.region_city)

            // 获取区县数据
            const districts = await this.getRegionDataWithCache(
              detailData.region_city,
              3
            )
            const district = districts?.find(
              (d) => d.value == detailData.region_district
            )

            if (province && city && district) {
              displayText = `${province.text}-${city.text}-${district.text}`
            } else {
              displayText = "地区信息加载中"
            }
          } catch (error) {
            console.error("获取地区显示文案失败:", error)
            displayText = "地区信息加载中"
          }
        } else {
          displayText = "地区信息加载中"
        }
        break

      case 3:
        // level=3: 只能编辑区级
        editable = true
        displayText = "请选择区县"
        break

      case 2:
        // level=2: 可编辑市区两级
        editable = true
        displayText = "请选择市区"
        break

      case 1:
      default:
        // level=1: 完整的三级联动
        editable = true
        displayText = "全部地区"
        break
    }

    this.setData({
      "positionRegionSelector.level": level,
      "positionRegionSelector.editable": editable,
      "positionRegionSelector.displayText": displayText,
      "positionRegionSelector.selectedValues": selectedValues,
      "positionRegionSelector.selectedIndexes": [], // 清空选中索引，确保初始状态正确
      // 清空preset数据，在showPositionRegionSelector中动态设置
      "positionRegionSelector.presetProvince": null,
      "positionRegionSelector.presetCity": null,
      "positionRegionSelector.presetDistrict": null,
    })
  },

  /**
   * 新增：根据ID在地区数据中查找对应的地区信息
   */
  findRegionById(regionList, id) {
    if (!regionList || !id) return null
    return regionList.find((item) => item.id == id) || null
  },

  /**
   * 新增：岗位报考查询地区选择器确认事件
   */
  onPositionRegionSelectorConfirm(e) {
    const { value, index } = e.detail
    const {
      level,
      presetProvince,
      presetCity,
      presetDistrict,
    } = this.data.positionRegionSelector

    let displayText = ""
    let selectedValues = []
    let selectedIndexes = []

    // 根据level处理不同的确认逻辑
    switch (level) {
      case 3:
        // level=3: 只选择了区县，只显示区县名称
        if (value && value.length > 0) {
          const selectedDistrict = value[0]
          displayText = selectedDistrict.text
          selectedValues = [
            presetProvince.id,
            presetCity.id,
            selectedDistrict.value,
          ]
          selectedIndexes = [index[0]] // 只保存区县的下标
        }
        break

      case 2:
        // level=2: 选择了市区，只显示市-区
        if (value && value.length >= 2) {
          const selectedCity = value[0]
          const selectedDistrict = value[1]
          displayText = `${selectedCity.text}-${selectedDistrict.text}`
          selectedValues = [
            presetProvince.id,
            selectedCity.value,
            selectedDistrict.value,
          ]
          selectedIndexes = [index[0], index[1]] // 保存市和区的下标
        }
        break

      case 1:
      default:
        // level=1: 完整三级联动
        if (value && value.length >= 3) {
          const selectedProvince = value[0]
          const selectedCity = value[1]
          const selectedDistrict = value[2]
          displayText = `${selectedProvince.text}-${selectedCity.text}-${selectedDistrict.text}`
          selectedValues = [
            selectedProvince.value,
            selectedCity.value,
            selectedDistrict.value,
          ]
          selectedIndexes = [index[0], index[1], index[2]] // 保存省市区的下标
        }
        break
    }

    const param = {
      region_province: selectedValues[0],
      region_city: selectedValues[1],
      region_district: selectedValues[2],
    }
    this.getWorkList(param)

    // 更新地区选择器状态
    this.setData({
      "positionRegionSelector.show": false,
      "positionRegionSelector.displayText": displayText,
      "positionRegionSelector.selectedValues": selectedValues,
      "positionRegionSelector.selectedIndexes": selectedIndexes,
    })
  },

  /**
   * 新增：岗位报考查询地区选择器取消事件
   */
  onPositionRegionSelectorClose() {
    this.setData({
      "positionRegionSelector.show": false,
    })
  },

  /**
   * 职位输入框输入事件
   */
  onPositionInput(event) {
    const value = event.detail.value

    this.setData({
      positionInput: value,
    })
  },

  onConfirm(event) {
    const { value } = event.detail
    const type = this.data.currentSelector
    const optList = this.data.optList
    const optKeys = this.data.optKeys

    const selectedIndex = optList.findIndex((item) => item == value)
    const selectedKey = optKeys[selectedIndex]
    const selectedText = value

    // 验证选中值是否有效
    if (selectedKey === undefined || !selectedText) {
      console.error("选中值无效:", selectedIndex, optKeys, optList)
      return
    }

    // 根据当前选择器类型更新对应的数据
    const updateData = {
      optPopShow: false,
      currentSelector: "",
      optList: [], // 清空选项列表
      optKeys: [], // 清空keys列表
      value: [], // 清空选中值
      defaultIndex: 0, // 清空默认索引
    }

    switch (type) {
      case "examType":
        updateData.selectedExamTypeKey = selectedKey
        updateData.selectedExamTypeText = selectedText
        break
      case "region":
        updateData.selectedRegionKey = selectedKey
        updateData.selectedRegionText = selectedText
        break
      case "specificExam":
        updateData.project_id = selectedKey
        updateData.project_name = selectedText
        break
      case "searchRegion":
        updateData.selectedSearchRegionText = selectedText
        break
      case "searchUnit":
        updateData.selectedSearchUnitKey = selectedKey
        break
      case "positionLocation":
        updateData.region_id = selectedKey
        updateData.region_name = selectedText
        break
      default:
        console.warn("未知的选择器类型:", type)
        return
    }

    this.setData(updateData)
    
    // 根据不同类型调用不同的更新方法
    if (type == "examType") {
      // 切换考试类型后，先更新地区选项，然后获取项目列表
      this.updateRegionOptionsWhenExamTypeChanged()
    } else if (type == "region") {
      // 切换地区后，重新获取项目列表
      this.getProjectList()
    } else if (type == "specificExam") {
      // 切换项目后，重新获取文章列表
      this.getArticleList()
    } else if (type == "positionLocation") {
      // 切换职位地区后，重新获取表格数据
      this.getCurrentTableData()
    }
  },

  /**
   * 考试类型改变时更新地区选项
   * 与H5保持一致的逻辑
   */
  async updateRegionOptionsWhenExamTypeChanged() {
    try {
      // 调用接口获取省份列表
      const provinceRes = await UTIL.request(API.getProvinceList, {
        exam_type: this.data.selectedExamTypeKey,
        source: "apply" // bigData.vue 使用 "apply"
      })

      if (provinceRes?.data && provinceRes.data.length > 0) {
        // 更新地区选项并默认选中第一个
        const regionOptions = provinceRes.data.map(item => ({
          text: item.area_name || item.region_name,
          value: item.id || item.region_id
        }))

        // 默认选中第一个省份
        let selectedRegionText = "所属地区"
        let selectedRegionKey = ""
        if (regionOptions.length > 0) {
          selectedRegionText = regionOptions[0].text
          selectedRegionKey = regionOptions[0].value
        }

        this.setData({
          regionOptions,
          selectedRegionText,
          selectedRegionKey,
          // 重置项目相关状态
          project_id: "",
          project_name: ""
        })

        // 调用 getProjectList 获取项目列表
        await this.getProjectList()
      }
    } catch (error) {
      console.error("获取省份列表失败:", error)
      wx.showToast({
        title: "获取省份列表失败，请重试",
        icon: "none"
      })
    }
  },

  /**
   * vant popup关闭
   */
  OptClose() {
    this.setData({
      optPopShow: false,
      currentSelector: "",
      defaultIndex: 0, // 清空默认索引
    })
  },

  /**
   * 返回按钮点击事件
   */
  onBackClick() {
    APP.backPage()
  },

  /**
   * 查看更多按钮点击事件
   */
  onLoadMoreClick() {
    // 增加显示数量
    const newDisplayCount = this.data.displayCount + 10

    this.setData(
      {
        displayCount: newDisplayCount,
      },
      () => {
        // 重新获取表格数据
        this.getCurrentTableData()
      }
    )
  },
  goDetail() {
    ROUTER.navigateTo({
      path: "/pages/notice/detail/index",
      query: {
        project_id: this.data.project_id,
        article_id: this.data.article_id
      }
    })
  },
  
  /**
   * 跳转到职位详情
   */
  goJobDetail(e) {
    const id = e.currentTarget.dataset.id
    if (!id) return
    
    ROUTER.navigateTo({
      path: "/pages/position/detail/index",
      query: {
        id: id,
        project_id: this.data.project_id
      }
    })
  },
  goFocus() {
    ROUTER.navigateTo({
      path: "/pages/my/focusList/index",
      query: {
        id: 2,
      },
    })
  },
  checkNews() {
    ROUTER.navigateTo({
      path: "/pages/notice/detail/index",
    })
  },
  goJobs() {
    ROUTER.navigateTo({
      path: "/pages/webview/activityCustomer/index",
      query: {
        event: "zKBr",
      },
    })
  },
  /**
   * 切换关注状态
   */
  async toggleFocus(e) {
    const index = e.currentTarget.dataset.index
    const job = this.data.jobData.list[index]
    if (!job) return
    
    const isFocused = job.is_follows == 1
    
    try {
      const res = await UTIL.request(API.setFocus, {
        job_id: job.id,
        is_follows: isFocused ? 0 : 1
      })
      
      if (res) {
        // 更新本地数据
        const key = `jobData.list[${index}].is_follows`
        this.setData({
          [key]: isFocused ? 0 : 1
        })
        
        wx.showToast({
          title: isFocused ? '取消关注成功' : '关注成功',
          icon: 'success',
          duration: 1500
        })
      }
    } catch (error) {
      console.error('切换关注失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },
  
  /**
   * 打开职位详情
   */
  openJob(e) {
    const job = e.currentTarget.dataset.job
    if (!job || !job.id) return
    
    ROUTER.navigateTo({
      path: "/pages/position/detail/index",
      query: {
        id: job.id,
        project_id: this.data.project_id
      }
    })
  },
  
  goWeb(e) {
    const query = wx.createSelectorQuery().in(this)
    query.select("#job-search-section").boundingClientRect()
    query.selectViewport().scrollOffset()
    query.select(".fixed-header").boundingClientRect()

    query.exec((res) => {
      if (res && res[0] && res[1] && res[2]) {
        const targetTop = res[0].top
        const scrollTop = res[1].scrollTop
        const headerRect = res[2]

        // 根据当前滚动位置判断头部是否应该显示
        const shouldShowHeader = scrollTop > 10
        let headerHeight = shouldShowHeader ? headerRect.height : 0

        // 如果头部不显示，需要减去状态栏高度 + 导航栏高度
        if (!shouldShowHeader) {
          // 导航栏高度 = 状态栏高度 + 44px（导航栏内容高度）
          headerHeight = this.data.statusBarHeight + 44
        }

        const finalScrollTop = scrollTop + targetTop - headerHeight

        wx.pageScrollTo({
          scrollTop: finalScrollTop,
          duration: 300,
        })
      }
    })
  },

  /**
   * 清除按钮点击事件
   */
  onClearClick() {
    this.setData({
      ["positionRegionSelector.selectedValues"]: [],
      selectedSearchRegionText: "全部地区",
      selectedSearchUnitKey: "",
      positionInput: "",
    })
  },

  /**
   * 数据说明展开收起切换
   */
  toggleDataDesc() {
    const expanded = this.data.dataDescExpanded
    this.setData({
      dataDescExpanded: !expanded,
    })
  },
  
  /**
   * 检查文本是否需要展开收起按钮
   */
  checkTextOverflow() {
    // 获取实际显示的文本内容（包括默认文本）
    const descText = this.data.detailData.desc || '本站发布的招考资讯均来源于招录官方网站，由事考帮整理编辑，如遇报考疑问请咨询招考官方，若涉及版权或错误信息，请提交反馈到本站予以更新或删除。数据仅供参考，具体请以官方公告为准。'
    
    // 如果文本超过100个字符，显示展开收起按钮
    const shouldShow = descText.length > 100
    
    this.setData({
      shouldShowExpandButton: shouldShow,
      dataDescExpanded: false // 默认收起状态
    })
  },
  
  /**
   * 统计卡片点击事件
   */
  onStatsCardClick(e) {
    const item = e.currentTarget.dataset.item
    if (item.type === 'normal') {
      return // normal类型不可点击
    }
    
    // 根据不同类型跳转到不同页面
    if (item.type === 'most_apply_work_unit') {
      // 跳转到招考单位TOP10
      this.scrollToRecruitingUnit()
    } else if (item.type === 'competitive_rate') {
      // 跳转到职位大数据的热门-按竞争比
      this.setData({
        currentMainTab: 'hot',
        selectedSubTab: 'competitive_rate',
        displayCount: 10
      }, () => {
        this.getCurrentTableData()
        this.scrollToPositionSection()
      })
    } else if (item.type === 'zero_apply_job') {
      // 跳转到职位大数据的冷门-按报名数
      this.setData({
        currentMainTab: 'cold',
        selectedSubTab: 'num',
        displayCount: 10
      }, () => {
        this.getCurrentTableData()
        this.scrollToPositionSection()
      })
    }
  },
  
  /**
   * 滚动到职位大数据区域
   */
  scrollToPositionSection() {
    const query = wx.createSelectorQuery().in(this)
    query.select('.position-table-section').boundingClientRect()
    query.selectViewport().scrollOffset()
    query.exec((res) => {
      if (res && res[0] && res[1]) {
        wx.pageScrollTo({
          scrollTop: res[1].scrollTop + res[0].top - this.data.statusBarHeight - 44,
          duration: 300
        })
      }
    })
  },
  
  /**
   * 滚动到招考单位TOP10区域
   */
  scrollToRecruitingUnit() {
    const query = wx.createSelectorQuery().in(this)
    query.select('.recruiting-unit').boundingClientRect()
    query.selectViewport().scrollOffset()
    query.exec((res) => {
      if (res && res[0] && res[1]) {
        wx.pageScrollTo({
          scrollTop: res[1].scrollTop + res[0].top - this.data.statusBarHeight - 44,
          duration: 300
        })
      }
    })
  },
  changeSort() {
    let sortIndex = this.data.sortIndex
    if (sortIndex === 2) {
      sortIndex = 0
    } else {
      sortIndex += 1
    }
    this.setData({
      sortIndex,
    })

    // 排序变化后重新查询（重置分页）
    this.onSearchClick()
  },

  onPageClick(e) {
    // 获取点击的元素路径
    const path = e.target.dataset.path || ""

    // 如果是点击了图表区域，不处理
    if (e.target.id === "mycharts" || path.includes("ec-canvas")) {
      return
    }

    // 关闭tooltip
    const chart = this.selectComponent("#score-bar-id-multiple")
    if (chart) {
      chart.hideTooltip()
    }
  },
  async getJobList(isLoadMore = false) {
    const { data } = this

    // 如果正在加载，直接返回
    if (data.pageParams.loading) {
      return
    }

    // 如果是加载更多但没有更多数据，直接返回
    if (isLoadMore && !data.pageParams.hasMore) {
      return
    }

    // 设置加载状态
    this.setData({
      "pageParams.loading": true,
    })

    const params = {
      project_id: data.project_id,
      article_id: data.article_id,
      order: data.sortIndex == 0 ? "" : data.sortIndex == 1 ? "asc" : "desc",
      work_unit: data.selectedSearchUnitKey,
      keywords: data.positionInput,
      region_province: data.positionRegionSelector.selectedValues[0] || "",
      region_city: data.positionRegionSelector.selectedValues[1] || "",
      region_district: data.positionRegionSelector.selectedValues[2] || "",
      page: data.pageParams.page,
      size: data.pageParams.size,
    }

    try {
      const res = await UTIL.request(API.getJobCompetitiveRateList, params)
      if (res && res.data) {
        const newList = res.data.list || []
        const totalCount = res.data.count || 0

        let updatedList = []
        if (isLoadMore) {
          // 加载更多：追加数据
          updatedList = [...data.jobData.list, ...newList]
        } else {
          // 首次加载：替换数据
          updatedList = newList
        }

        // 判断是否还有更多数据
        const hasMore = updatedList.length < totalCount

        this.setData({
          jobData: {
            count: totalCount,
            list: updatedList,
          },
          "pageParams.hasMore": hasMore,
          "pageParams.page": data.pageParams.page + 1,
        })
      }
    } catch (error) {
      console.error("获取职位列表失败:", error)
    } finally {
      // 取消加载状态
      this.setData({
        "pageParams.loading": false,
      })
    }
  },

  /**
   * 查询按钮点击事件
   */
  onSearchClick() {
    // 重置分页参数，进行首次查询
    this.setData({
      "pageParams.page": 1,
      "pageParams.hasMore": true,
      "pageParams.loading": false,
      hasSearched: true, // 标记已经进行过查询
    })

    // 调用获取职位列表方法（首次查询）
    this.getJobList(false)
  },

  /**
   * 页面触底加载更多
   */
  onReachBottom() {
    // 只有在已经进行过查询且有数据的情况下才允许加载更多
    if (!this.data.hasSearched || this.data.jobData.list.length === 0) {
      return
    }

    // 触发加载更多
    this.getJobList(true)
  },

  /**
   * 新增：预加载地区数据
   */
  async preloadRegionData(detailData) {
    const level = detailData?.level

    try {
      switch (level) {
        case 4:
          // level=4不可编辑，无需预加载
          break

        case 3:
          // level=3只需要预加载区县数据
          if (detailData.region_city) {
            await this.getRegionDataWithCache(detailData.region_city, 3)
          }
          break

        case 2:
          // level=2需要预加载城市和第一个城市的区县
          if (detailData.region_province) {
            const cities = await this.getRegionDataWithCache(
              detailData.region_province,
              2
            )
            // 预加载第一个城市的区县
            if (cities && cities.length > 0) {
              await this.getRegionDataWithCache(cities[0].value, 3)
            }
          }
          break

        case 1:
        default:
          // level=1需要预加载省份、第一个省份的城市、第一个城市的区县
          const provinces = await this.getRegionDataWithCache(0, 1)
          if (provinces && provinces.length > 0) {
            // 预加载第一个省份的城市
            const cities = await this.getRegionDataWithCache(
              provinces[0].value,
              2
            )
            if (cities && cities.length > 0) {
              // 预加载第一个城市的区县
              await this.getRegionDataWithCache(cities[0].value, 3)
            }
          }
          break
      }
    } catch (error) {
      console.error("预加载地区数据失败:", error)
    }
  },
})
