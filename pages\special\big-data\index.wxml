<wxs module="utils" src="./index.wxs"></wxs>
<view class="big-data" bindtap="onPageClick" data-path="root">
  <!-- 固定头部导航栏 -->
  <view class="fixed-header {{showFixedHeader ? 'show' : 'hide'}}" style="padding-top: {{statusBarHeight}}px;">
    <view class="header-content">
      <view class="header-left" catch:tap="onBackClick">
        <view class="back-icon"></view>
      </view>
      <view class="header-title">报名大数据</view>
      <view class="header-right"></view>
    </view>
  </view>

  <image class="back-btn" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data-top_back.png" mode="" catch:tap="onBackClick" />
  <view class="top-bg">
    <image class="bg-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_bg.png" mode="" />
    <view class="top-area">
      <image class="top-title-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_title.png" mode="" />
      <view class="title">最后更新时间：{{updateTime || '2025.06.12 15:35'}}</view>
      <view class="select-one">
        <view class="select-item" bindtap="onExamTypeClick">
          <view class="text">{{selectedExamTypeText}}</view>
          <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
        </view>
        <view class="select-item" bindtap="onRegionClick">
          <view class="text">{{selectedRegionText}}</view>
          <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
        </view>
      </view>
      <view class="select-item w100" bindtap="onSpecificExamClick">
        <view class="text">{{project_name}}</view>
        <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
      </view>
    </view>
  </view>
  <view class="main-contnet">
    <!-- Tab导航区域 -->
    <view class="tab-container">
      <scroll-view class="tab-scroll" scroll-x="true" scroll-with-animation="true" scroll-left="{{scrollLeft}}" show-scrollbar="{{false}}" enhanced="{{true}}">
        <view class="tab-list">
          <view wx:for="{{articleList}}" wx:key="index" class="tab-item {{currentTab === index ? 'active' : ''}}" data-index="{{index}}" data-id="{{item.id}}" bindtap="onTabClick" id="tab-{{index}}">
            {{item.title}}
          </view>
        </view>
      </scroll-view>
    </view>
    <!-- 内容区域 -->
    <view class="content-container">
      <view class="content-area">
        <!-- 招聘公告标题 -->
        <view class="announcement-title" catch:tap="goDetail">
          <text class="text"> {{detailData.title}}</text>
          <image class="arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/announcement_title_arrow.png" mode="" />
        </view>

        <!-- 数据统计卡片 -->
        <view class="stats-container" wx:if="{{detailData.sector_list && detailData.sector_list.length}}">
          <view wx:for="{{detailData.sector_list}}" wx:key="index" class="stats-card large-card {{utils.getCardClass(index)}}" bindtap="onStatsCardClick" data-item="{{item}}">
            <view class="card-title">
              {{item.title}}
              <image wx:if="{{item.type != 'normal'}}" class="arrow-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_right_1.png" mode="" />
            </view>
            <view class="card-number">
              <text class="number" wx:if="{{item.type == 'competitive_rate'}}">
                {{item.value || '-'}}
              </text>
              <text class="number" wx:else>
                {{item.value && item.value > 0 ? utils.formattedNumber(item.value) : '-'}}
              </text>
              <text class="unit" wx:if="{{item.value && item.value > 0}}">{{item.unit}}</text>
              <view class="card-type" wx:if="{{item.type == 'competitive_rate' && item.tag && item.value}}">
                {{item.tag}}
              </view>
              <view class="card-type" wx:elif="{{item.tag && item.value && item.value > 0}}">
                {{item.tag}}
              </view>
            </view>
            <view class="card-desc" wx:if="{{item.type == 'competitive_rate' && item.desc && item.value}}">
              {{item.desc}}
            </view>
            <view class="card-desc" wx:elif="{{item.desc && item.value && item.value > 0}}">
              {{item.desc}}
            </view>
          </view>
        </view>

        <view class="data-desc">
          <view class="data-desc-content {{dataDescExpanded ? '' : 'collapsed'}}">
            <text class="data-desc-text">
              数据说明：{{detailData.desc || '本站发布的招考资讯均来源于招录官方网站，由事考帮整理编辑，如遇报考疑问请咨询招考官方，若涉及版权或错误信息，请提交反馈到本站予以更新或删除。数据仅供参考，具体请以官方公告为准。'}}
              <text wx:if="{{dataDescExpanded && shouldShowExpandButton}}" class="expand-btn inline" bindtap="toggleDataDesc">
                <text class="blue-text">收起</text>
                <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_down.png" class="arrow-icon rotated" mode="" />
              </text>
            </text>
            <text wx:if="{{!dataDescExpanded && shouldShowExpandButton}}" class="expand-btn" bindtap="toggleDataDesc">
              <text class="blue-text">展开</text>
              <image src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_down.png" class="arrow-icon" mode="" />
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 各地域报名数据 -->
  <view class="area-box" wx:if="{{detailData.region_num_list && detailData.region_num_list.length > 1}}">
    <view class="title">各地域报名数据</view>
    <view catch:tap>
      <score-all-echarts id="score-bar-id-multiple" scoreData="{{detailData.region_num_list}}"></score-all-echarts>
    </view>
  </view>

  <!-- 职位数据表格区域 -->
  <view class="position-table-section" wx:if="{{currentTableData.length}}">
    <!-- 标题区域 -->
    <view class="position-header">
      <view class="position-title">职位大数据</view>
      <view class="position-location" wx:if="{{detailData.region_num_list && detailData.region_num_list.length > 1}}" bindtap="onPositionLocationClick">
        <text class="location-text">{{region_name}}</text>
        <image class="location-arrow" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_gray_select.png" mode="" />
      </view>
    </view>

    <!-- Tab导航 -->
    <view class="position-tab-container">
      <view class="position-tab-list">
        <view class="position-tab-item {{currentMainTab === 'hot' ? 'active' : ''}}" data-index="hot" bindtap="onMainTabClick">
          热门
        </view>
        <view class="position-tab-item {{currentMainTab === 'cold' ? 'active' : ''}}" data-index="cold" bindtap="onMainTabClick">
          冷门
        </view>
      </view>
      <view class="position-sub-tab-list">
        <view class="position-sub-tab-item {{selectedSubTab === 'num' ? 'active' : ''}}" data-type="num" bindtap="onSubTabClick">
          按{{utils.getMainTypeText(detailData.show_filed)}}数
        </view>
        <view class="position-sub-tab-item {{selectedSubTab === 'competitive_rate' ? 'active' : ''}}" data-type="competitive_rate" bindtap="onSubTabClick">
          按竞争比
        </view>
      </view>
    </view>

    <!-- 表格1内容 -->
    <view class="position-table-content">
      <!-- 表格头部 -->
      <view class="position-table-header">
        <view class="header-item header-rank"></view>
        <view class="header-item header-position">
          <text>职位</text>
          <text>名称</text>
        </view>
        <view class="header-item header-unit">
          <text>招考</text>
          <text>单位</text>
        </view>
        <view class="header-item header-recruit">
          <text>招录</text>
          <text>人数</text>
        </view>
        <view class="header-item header-apply" wx:if="{{selectedSubTab === 'num'}}">
          <text>{{utils.getMainTypeText(detailData.show_filed)}}</text>
          <text>人数</text>
        </view>
        <view class="header-item header-apply" wx:else>
          <text>竞争</text>
          <text>比</text>
        </view>
      </view>

      <!-- 表格数据 -->
      <view class="position-table-body">
        <view wx:for="{{currentTableData}}" wx:key="index" class="position-table-row">
          <view class="table-item table-rank">
            <view class="rank-badge rank-{{index + 1}}">{{index + 1}}</view>
          </view>
          <view class="table-item table-position" bindtap="goJobDetail" data-id="{{item.id}}">
            <text class="position-title">{{item.name}}</text>
          </view>
          <view class="table-item table-unit">
            <text class="unit-title">{{item.work_unit}}</text>
          </view>
          <view class="table-item table-recruit">
            <text class="recruit-num">{{item.need_num}}</text>
          </view>
          <view class="table-item table-apply">
            <text class="apply-num" wx:if="{{selectedSubTab === 'num'}}">{{item.num}}</text>
            <text class="apply-num" wx:else>{{item.competitive_rate}}</text>
          </view>
        </view>
      </view>

      <!-- 查看更多 -->
      <view class="load-more-section" wx:if="{{showLoadMore}}" bindtap="onLoadMoreClick">
        <view class="load-more-text">查看更多</view>
        <image class="load-more-icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_gray_select.png" mode="" />
      </view>
    </view>

  </view>

  <view class="recruiting-unit">
    <view class="recruit-title">
      招考单位 TOP10
    </view>
    <!-- 表格2内容 -->
    <view class="position-table-content-2">
      <!-- 表格头部 -->
      <view class="position-table-header">
        <view class="header-item header-rank"></view>
        <view class="header-item header-position">
          <text>招考</text>
          <text>单位</text>
        </view>
        <view class="header-item header-unit">
          <text>职位</text>
          <text>数</text>
        </view>
        <view class="header-item header-recruit">
          <text>招录</text>
          <text>人数</text>
        </view>
        <view class="header-item header-apply">
          <text>{{utils.getMainTypeText(detailData.show_filed)}}</text>
          <text>人数</text>
        </view>
        <view class="header-item header-competition">
          <text>竞争</text>
          <text>比例</text>
        </view>
      </view>

      <!-- 表格数据 -->
      <view class="position-table-body">
        <view wx:for="{{detailData.work_unit_list}}" wx:key="index" class="position-table-row">
          <view class="table-item table-rank">
            <view class="rank-badge rank-{{index + 1}}">{{index + 1}}</view>
          </view>
          <view class="table-item table-position">
            <text class="position-title">{{item.work_unit}}</text>
          </view>
          <view class="table-item table-unit">
            <text class="unit-title">{{item.job_num}}</text>
          </view>
          <view class="table-item table-recruit">
            <text class="recruit-num">{{item.need_num}}</text>
          </view>
          <view class="table-item table-apply">
            <text class="apply-num">{{item.num}}</text>
          </view>
          <view class="table-item table-competition">
            <text class="competition-rate">{{item.competitive_rate}}</text>
          </view>
        </view>
      </view>
    </view>
    <image class="zhaokao-bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/zhaokao-bg.png" mode="" />
  </view>

  <view class="job-search" id="job-search-section">
    <view class="title">岗位报考查询</view>
    <view class="search-box">
      <image class="bg" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/search_box_bg.png" mode="" />
      <!-- <view class="top-area">
        <image class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/search_box_top_icon.png" mode="" />
        <view class="text">2025年重庆市公务员</view>
      </view> -->
      <view class="white-box">
        <view class="select-item {{!positionRegionSelector.editable ? 'disabled' : ''}}" bindtap="onPositionRegionClick">
          <view class="left">
            <view class="sp5">
              <text>地</text>
              <text>区：</text>
            </view>
            <view>{{positionRegionSelector.displayText}}</view>
          </view>
          <image class="right" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
        </view>
        <view class="select-item" bindtap="onSearchUnitClick">
          <view class="left">
            <view class="sp5">
              用人单位：
            </view>
            <view>{{selectedSearchUnitKey || '全部'}}</view>
          </view>
          <image class="right" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png" mode="" />
        </view>
        <view class="select-item input-item">
          <view class="left">
            <view class="sp5">
              <text>职</text>
              <text>位：</text>
            </view>
            <input class="position-input" type="text" placeholder="请输入职位名称或代码" value="{{positionInput}}" bindinput="onPositionInput" placeholder-class="input-placeholder" />
          </view>
        </view>
        <view class="button-row">
          <view class="clear-btn" bindtap="onClearClick">清除</view>
          <view class="search-btn" bind:tap="onSearchClick">
            <image class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/search_box_search_icon.png" mode="" />
            <text>查询</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <view class="result-area" wx:if="{{ jobData.count > 0 }}">
    <view class="top-area">
      <view class="left">
        <view class="text-area">
          <text>共</text>
          <text class="red">{{jobData.count}}</text>
          <text>个职位</text>
        </view>
        <view class="btn" bind:tap="changeSort">
          <view class="text">竞争比</view>
          <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/{{sortIndex === 1?'arrow_top':sortIndex === 2?'arrow_bottom':'arrow_all'}}.png" mode="" />
        </view>
      </view>
      <view class="right" bind:tap="goFocus">
        <view class="text">我的关注</view>
        <image class="icon" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_right_blue.png" mode="" />
      </view>
    </view>
    <view wx:for="{{ jobData.list }}" wx:key="index">
      <job-card jobData="{{item}}" projectId="{{project_id}}"></job-card>
    </view>
  </view>

  <tabbar-box>
    <view class="bottom-box" wx:if="{{false}}">
      <view class="left">
        <view class="icon-item" bind:tap="goJobs">
          <image class="icon-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/job_service.png" mode="" />
          <view class="icon-text">选岗咨询</view>
        </view>
        <view class="icon-item" bind:tap="checkNews">
          <image class="icon-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/news.png" mode="" />
          <view class="icon-text">查看公告</view>
        </view>
      </view>
      <view class="right-btn" bind:tap="goWeb">岗位报考查询</view>
    </view>
  </tabbar-box>

  <!-- 下拉框弹窗 -->
  <van-popup show="{{ optPopShow }}" round position="bottom" bind:close="OptClose">
    <van-picker id="myPicker" show-toolbar title="{{ optTitle }}" columns="{{ optList }}" value-key="key" columns-field-names="{{ {text: 'name', value: 'key'} }}" default-index="{{ defaultIndex }}" toolbar-class="top-area" column-class="column-area" active-class="active-item" bind:cancel="OptClose" bind:confirm="onConfirm" />
  </van-popup>

  <!-- 岗位报考查询地区选择器 -->
  <van-popup show="{{ positionRegionSelector.show }}" round position="bottom" z-index="9999" bind:close="onPositionRegionSelectorClose">
    <van-picker id="positionRegionPicker" show-toolbar title="{{ positionRegionSelector.title }}" columns="{{ positionRegionSelector.columns }}" toolbar-class="top-area" column-class="column-area" active-class="active-item" bind:cancel="onPositionRegionSelectorClose" bind:confirm="onPositionRegionSelectorConfirm" bind:change="onPositionRegionSelectorChange" />
  </van-popup>
</view>

<!-- 底部导航栏 -->
<!-- <home-tabbar active="my" /> -->