const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const APP = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    navigationBarHeight: "",
    tabHeight: "",
    isLogin: true,
    resumeProgress: 23, // 简历完善度
    activeTabIndex: 1,
    subActiveTabIndex: 0,
    tipsShow: true,
    isSticky: false,
    baseInfo: null,
    tabList: [
      {
        title: "关注公告",
        subList: [],
      },
      {
        title: "关注职位",
        subList: [],
      },
      {
        title: "浏览足迹",
        subList: [
          {
            title: "公告",
          },
          {
            title: "职位",
          },
        ],
      },
    ],
    dataList: [],
    // 弹窗相关
    showOptionsPopup: false, // 显示操作选项弹窗
    showTagPopup: false, // 显示添加标签弹窗
    currentJobInfo: null, // 当前操作的职位信息
    // 一级弹窗选项数据
    optionsData: [],
    // 二级弹窗选项数据
    tagOptionsData: [],
    page: 1,
    size: 20,
    tips: "",
    pkListIds: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    await APP.checkLoadRequest()
    this.setData({
      baseInfo: APP.globalData.userInfo,
    })
    this.getHeight()
  },
  getConfig() {
    const data = APP.globalData?.serverConfig?.job_tag || []
    let tagOptionsData = []
    const follows_tag = this.data.currentJobInfo.follows_tag
    if (data.length) {
      tagOptionsData = data.map((item) => {
        if (follows_tag == item.key) {
          return {
            name: "取消" + item.name,
            value: item.key,
          }
        } else {
          return {
            name: "加入" + item.name,
            value: item.key,
          }
        }
      })
    }
    this.setData({
      tagOptionsData,
    })
  },
  async getList() {
    this.setData({ dataList: [] })
    const url =
      this.data.activeTabIndex == 2 ? API.getFootPrintList : API.getFollowsList
    const param = {
      item_type: this.data.activeTabIndex == 0 ? "article" : "job",
      page: this.data.page,
      size: this.data.size,
    }
    if (this.data.activeTabIndex == 2) {
      param.item_type = this.data.subActiveTabIndex == 0 ? "article" : "job"
    }
    const res = await UTIL.request(url, param)
    let dataList = []
    let tips = ""
    if (this.data.activeTabIndex == 2) {
      dataList = res?.data?.list || []
      tips = res?.data?.tips || ""
    } else {
      dataList = res?.data || []
    }
    this.setData({
      dataList,
      tips,
    })
  },
  getHeight() {
    wx.createSelectorQuery()
      .select(".tab-area")
      .boundingClientRect((res) => {
        const tabHeight = res.top
        this.setData({
          tabHeight,
        })
      })
      .exec()
    let menuInfo = wx.getMenuButtonBoundingClientRect()
    this.setData({
      navigationBarHeight: menuInfo.top + 32,
    })
  },
  onPageScroll(e) {
    const scrollTop = e.scrollTop
    if (scrollTop > this.data.tabHeight && !this.data.isSticky) {
      this.setData({
        isSticky: true,
      })
    }
    if (scrollTop < this.data.tabHeight && this.data.isSticky) {
      this.setData({
        isSticky: false,
      })
    }
  },
  // 处理职位卡片的三个点点击事件
  onJobOptionsClick(e) {
    let optionsData = [
      { name: "添加标签", value: "addTag" },
      { name: "加入对比", value: "addToCompare" },
      { name: "取消关注", value: "unfollow" },
    ]
    const { jobId, job_name, follows_id, follows_tag } = e.detail
    if (follows_tag) {
      optionsData[0].name = "修改标签"
    }
    if (this.data.pkListIds.length && this.data.pkListIds.includes(jobId)) {
      optionsData = [
        { name: "添加标签", value: "addTag" },
        { name: "取消关注", value: "unfollow" },
      ]
    }
    this.setData({
      currentJobInfo: {
        jobId: jobId,
        job_name: job_name,
        follows_id: follows_id,
        follows_tag: follows_tag,
      },
      optionsData,
      showOptionsPopup: true,
    })
  },
  goResume() {
    ROUTER.navigateTo({
      path: "/pages/my/resume/index",
    })
  },
  changeIndex(e) {
    const { index } = e.currentTarget.dataset
    if (index == this.data.activeTabIndex) {
      return
    }
    this.setData({
      activeTabIndex: index,
    })
    this.getList()
  },
  changeSubIndex(e) {
    const { index } = e.currentTarget.dataset
    this.setData({
      subActiveTabIndex: index,
    })
    this.getList()
  },

  closetip() {
    this.setData({
      tipsShow: false,
    })
  },

  // 关闭操作选项弹窗
  closeOptionsPopup() {
    this.setData({
      showOptionsPopup: false,
    })
  },

  // 关闭添加标签弹窗
  closeTagPopup() {
    this.setData({
      showTagPopup: false,
    })
  },

  // 处理一级弹窗选择事件
  onOptionsSelect(e) {
    const { value } = e.detail
    this.setData({
      showOptionsPopup: false,
    })

    switch (value) {
      case "addTag":
        this.getConfig()
        this.setData({
          showTagPopup: true,
        })
        break
      case "addToCompare":
        this.addToCompare()
        break
      case "unfollow":
        wx.showModal({
          title: "确认取消关注",
          content: `确定要取消关注"${this.data.currentJobInfo?.job_name}"吗？`,
          success: (res) => {
            if (res.confirm) {
              UTIL.request(API.setFollows, {
                item_type: "job",
                item_no: [this.data.currentJobInfo.jobId],
                type: "unfollow",
              }).then((res) => {
                if (res) {
                  wx.showToast({
                    title: "已取消关注",
                    icon: "none",
                  })
                  this.getList()
                }
              })
            }
          },
        })
        break
    }
  },

  // 处理二级弹窗选择事件
  async onTagSelect(e) {
    const { value } = e.detail
    const param = {
      follows_id: this.data.currentJobInfo.follows_id,
      tag: value,
    }
    const res = await UTIL.request(API.setFollowTag, param)
    if (res) {
      wx.showToast({
        title:
          value == this.data.currentJobInfo.follows_tag
            ? "取消成功"
            : "加入成功",
        icon: "none",
      })
      this.setData({
        showTagPopup: false,
      })
      this.getList()
    }

    let toastTitle = ""
    switch (value) {
      case "addToApplication":
        toastTitle = "已加入报考岗位"
        break
      case "addToIntention":
        toastTitle = "已加入意向岗位"
        break
    }

    if (toastTitle) {
      wx.showToast({
        title: toastTitle,
        icon: "none",
      })
    }
  },

  addToCompare() {
    const newPkListIds = this.data.pkListIds
    if (!newPkListIds.includes(this.data.currentJobInfo.jobId)) {
      newPkListIds.push(this.data.currentJobInfo.jobId)
    }
    wx.setStorageSync("pkListIds", newPkListIds)
    this.setData({
      pkListIds: newPkListIds,
    })
    wx.showToast({
      title: "已加入对比",
      icon: "none",
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getPkListIds()
    this.getList()
  },

  getPkListIds() {
    const pkListIds = wx.getStorageSync("pkListIds") || []
    this.setData({
      pkListIds,
    })
  },
  goComparison() {
    ROUTER.navigateTo({
      path: "/pages/job/comparisonList/index",
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
})
